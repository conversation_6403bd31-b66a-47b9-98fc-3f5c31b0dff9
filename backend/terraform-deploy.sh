#!/bin/bash

# GameFlex Backend Terraform Deployment Script
# This script replaces the CDK deploy.sh script with Terraform-based deployment

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_error() {
    echo -e "${RED}❌ ERROR: $1${NC}" >&2
}

print_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

print_status() {
    echo -e "${BLUE}ℹ️  INFO: $1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$SCRIPT_DIR/terraform"
PROJECT_NAME="gameflex"

# Default values
ENVIRONMENT="development"
SKIP_CONFIRMATION=false
SHOW_PLAN=false
PLAN_ONLY=false
DESTROY=false
VERBOSE=false
AUTO_APPROVE=false
CLEAN_INIT=false

# Usage function
usage() {
    cat << EOF
Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENT:
    development     Deploy to development environment (default)
    staging         Deploy to staging environment
    production      Deploy to production environment

OPTIONS:
    -y, --yes           Skip confirmation prompts (auto-approve)
    -p, --plan          Show Terraform plan before applying
    --plan-only         Only show plan, don't apply
    --destroy           Destroy infrastructure instead of creating
    --clean             Force clean initialization (removes .terraform directory)
    -v, --verbose       Enable verbose output
    -h, --help          Show this help message

EXAMPLES:
    $0                          # Deploy to development
    $0 staging                  # Deploy to staging
    $0 production -p            # Show plan for production
    $0 development --destroy    # Destroy development infrastructure
    $0 staging -y               # Deploy to staging without confirmation
    $0 production --clean       # Clean init and deploy to production
    $0 staging --clean -y       # Clean init and deploy to staging (auto-approve)

ENVIRONMENT VARIABLES:
    AWS_PROFILE         AWS profile to use (optional)
    AWS_REGION          AWS region (will be set from environment config)
    TF_LOG              Terraform log level (DEBUG, INFO, WARN, ERROR)

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            development|staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            -y|--yes)
                SKIP_CONFIRMATION=true
                AUTO_APPROVE=true
                shift
                ;;
            -p|--plan)
                SHOW_PLAN=true
                shift
                ;;
            --plan-only)
                PLAN_ONLY=true
                SHOW_PLAN=true
                shift
                ;;
            --destroy)
                DESTROY=true
                shift
                ;;
            --clean)
                CLEAN_INIT=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            print_status "Environment: $ENVIRONMENT"
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install Terraform first."
        print_info "Visit: https://www.terraform.io/downloads.html"
        exit 1
    fi
    
    # Check Terraform version
    local tf_version=$(terraform version -json | jq -r '.terraform_version')
    print_status "Terraform version: $tf_version"
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured or invalid."
        print_error "Please configure AWS credentials using 'aws configure' or set environment variables."
        exit 1
    fi
    
    # Check if jq is installed (for JSON parsing)
    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed. Please install jq for JSON parsing."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Set environment variables
set_environment_variables() {
    print_status "Setting environment variables for $ENVIRONMENT..."
    
    # Set AWS region based on environment
    case $ENVIRONMENT in
        development|staging)
            export AWS_REGION="us-west-2"
            ;;
        production)
            export AWS_REGION="us-east-1"
            ;;
    esac
    
    # Get AWS account ID
    export AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    
    print_status "AWS Region: $AWS_REGION"
    print_status "AWS Account ID: $AWS_ACCOUNT_ID"
    
    # Set Terraform log level if verbose
    if $VERBOSE; then
        export TF_LOG="DEBUG"
    fi
}

# Clean Terraform initialization
clean_terraform_init() {
    print_status "Performing clean Terraform initialization..."

    if [[ -d ".terraform" ]]; then
        print_status "Removing existing .terraform directory..."
        rm -rf ".terraform"
    fi

    if [[ -f ".terraform.lock.hcl" ]]; then
        print_status "Removing existing .terraform.lock.hcl file..."
        rm -f ".terraform.lock.hcl"
    fi

    if [[ -f "tfplan" ]]; then
        print_status "Removing existing tfplan file..."
        rm -f "tfplan"
    fi
}

# Check if backend configuration has changed
check_backend_config_change() {
    local backend_config="backend-configs/${ENVIRONMENT}.hcl"
    local terraform_dir=".terraform"
    local backend_state_file="$terraform_dir/terraform.tfstate"

    # If no .terraform directory exists, this is a fresh init
    if [[ ! -d "$terraform_dir" ]]; then
        return 1  # Fresh init needed
    fi

    # If backend state file doesn't exist, reconfigure needed
    if [[ ! -f "$backend_state_file" ]]; then
        return 0  # Reconfigure needed
    fi

    # Check if the current backend config matches what's expected
    # This is a simple check - in practice, you might want more sophisticated detection
    local current_env_marker="$terraform_dir/.current_environment"
    if [[ -f "$current_env_marker" ]]; then
        local stored_env=$(cat "$current_env_marker")
        if [[ "$stored_env" != "$ENVIRONMENT" ]]; then
            return 0  # Reconfigure needed - environment changed
        fi
    else
        return 0  # Reconfigure needed - no environment marker
    fi

    return 1  # No reconfigure needed
}

# Initialize Terraform
terraform_init() {
    print_status "Initializing Terraform..."

    cd "$TERRAFORM_DIR"

    # Initialize with backend configuration
    local backend_config="backend-configs/${ENVIRONMENT}.hcl"

    if [[ ! -f "$backend_config" ]]; then
        print_error "Backend configuration not found: $backend_config"
        exit 1
    fi

    # Perform clean initialization if requested
    if $CLEAN_INIT; then
        clean_terraform_init
    fi

    # Check if backend configuration has changed or clean init was requested
    if $CLEAN_INIT || check_backend_config_change; then
        print_status "Backend configuration change detected or clean init requested, reconfiguring for $ENVIRONMENT..."
        terraform init -backend-config="$backend_config" -reconfigure -upgrade
    else
        print_status "Initializing Terraform for $ENVIRONMENT..."
        terraform init -backend-config="$backend_config" -upgrade
    fi

    local init_exit_code=$?

    # If init failed, try with reconfigure
    if [[ $init_exit_code -ne 0 ]]; then
        print_warning "Initial terraform init failed, trying with -reconfigure..."
        terraform init -backend-config="$backend_config" -reconfigure -upgrade
        init_exit_code=$?
    fi

    if [[ $init_exit_code -ne 0 ]]; then
        print_error "Terraform initialization failed"
        print_error "Try manually running: cd terraform && terraform init -backend-config=$backend_config -reconfigure"
        exit 1
    fi

    # Store current environment for future reference
    echo "$ENVIRONMENT" > ".terraform/.current_environment"

    print_success "Terraform initialized successfully"
}

# Validate Terraform configuration
terraform_validate() {
    print_status "Validating Terraform configuration..."
    
    terraform validate
    
    if [[ $? -ne 0 ]]; then
        print_error "Terraform validation failed"
        exit 1
    fi
    
    print_success "Terraform configuration is valid"
}

# Plan Terraform changes
terraform_plan() {
    print_status "Planning Terraform changes..."
    
    local var_file="environments/${ENVIRONMENT}.tfvars"
    
    if [[ ! -f "$var_file" ]]; then
        print_error "Variables file not found: $var_file"
        exit 1
    fi
    
    local plan_args="-var-file=$var_file"
    plan_args="$plan_args -var=aws_account_id=$AWS_ACCOUNT_ID"
    plan_args="$plan_args -var=aws_region=$AWS_REGION"
    
    if $DESTROY; then
        terraform plan -destroy $plan_args -out=tfplan
    else
        terraform plan $plan_args -out=tfplan
    fi
    
    if [[ $? -ne 0 ]]; then
        print_error "Terraform planning failed"
        exit 1
    fi
    
    print_success "Terraform plan completed"
}

# Apply Terraform changes
terraform_apply() {
    if $PLAN_ONLY; then
        print_status "Plan-only mode enabled. Skipping apply."
        return 0
    fi
    
    local action="apply"
    if $DESTROY; then
        action="destroy"
    fi
    
    print_status "Applying Terraform changes ($action)..."
    
    local apply_args=""
    if $AUTO_APPROVE; then
        apply_args="-auto-approve"
    fi
    
    terraform apply $apply_args tfplan
    
    if [[ $? -ne 0 ]]; then
        print_error "Terraform $action failed"
        exit 1
    fi
    
    print_success "Terraform $action completed successfully"
}

# Show outputs
show_outputs() {
    if $DESTROY || $PLAN_ONLY; then
        return 0
    fi
    
    print_status "Terraform outputs:"
    terraform output -json | jq '.'
}

# Confirmation prompt
confirm_deployment() {
    if $SKIP_CONFIRMATION; then
        return 0
    fi
    
    echo ""
    if $DESTROY; then
        print_warning "⚠️  DESTRUCTIVE OPERATION ⚠️"
        print_warning "You are about to DESTROY infrastructure in the $ENVIRONMENT environment"
        print_warning "This action cannot be undone!"
    else
        print_warning "You are about to deploy to the $ENVIRONMENT environment"
        print_warning "This will create/update AWS resources which may incur costs"
    fi
    echo ""
    
    local action="deploy"
    if $DESTROY; then
        action="destroy"
    fi
    
    read -p "Do you want to continue with $action? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled"
        exit 0
    fi
}

# Build Lambda functions
build_lambda_functions() {
    if $DESTROY || $PLAN_ONLY; then
        return 0
    fi

    print_status "Building Lambda functions..."

    cd "$SCRIPT_DIR"

    # Step 1: Install dependencies for Lambda functions first
    for lambda_dir in src/*/; do
        if [[ -f "${lambda_dir}package.json" ]]; then
            print_status "Installing dependencies for $(basename $lambda_dir)..."
            (cd "$lambda_dir" && npm install --production --silent)
        fi
    done

    # Step 2: Copy utils to all Lambda functions
    for lambda_dir in src/*/; do
        if [[ -d "$lambda_dir" && -d "src/utils" ]]; then
            local func_name=$(basename "$lambda_dir")
            print_status "Copying utils to $func_name..."
            cp -r src/utils "$lambda_dir/" 2>/dev/null || true
        fi
    done

    # Step 3: Build the full TypeScript project
    if [[ -f "package.json" ]]; then
        npm run build
        if [[ $? -ne 0 ]]; then
            print_error "Failed to build TypeScript project"
            exit 1
        fi
    fi

    print_success "Lambda functions built successfully"
}

# Main execution function
main() {
    print_info "🚀 GameFlex Backend Terraform Deployment"
    print_info "=========================================="
    
    parse_args "$@"
    validate_environment
    check_prerequisites
    set_environment_variables
    build_lambda_functions
    terraform_init
    terraform_validate
    terraform_plan
    
    if $SHOW_PLAN; then
        print_status "Terraform plan completed. Review the changes above."
    fi
    
    confirm_deployment
    terraform_apply
    show_outputs
    
    if $DESTROY; then
        print_success "🎯 Infrastructure destroyed successfully for $ENVIRONMENT environment"
    else
        print_success "🎉 Deployment completed successfully for $ENVIRONMENT environment"
    fi
}

# Run main function with all arguments
main "$@"
