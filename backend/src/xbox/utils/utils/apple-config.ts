import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

// Types
interface AppleConfig {
    teamId: string;
    clientId: string;
    keyId: string;
    privateKey: string;
}

// Cache for Apple configuration to avoid repeated Secrets Manager calls
let appleConfigCache: AppleConfig | null = null;
let cacheTimestamp: number | null = null;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get Apple Sign In configuration from AWS Secrets Manager
 */
async function getAppleConfig(): Promise<AppleConfig> {
    const secretName = process.env.APPLE_CONFIG_SECRET_NAME;

    if (!secretName) {
        // Fallback to environment variables for backward compatibility
        console.warn('APPLE_CONFIG_SECRET_NAME not found, falling back to environment variables');
        return {
            teamId: process.env.APPLE_TEAM_ID || '',
            clientId: process.env.APPLE_CLIENT_ID || '',
            keyId: process.env.APPLE_KEY_ID || '',
            privateKey: process.env.APPLE_PRIVATE_KEY || ''
        };
    }

    // Check cache first
    const now = Date.now();
    if (appleConfigCache && cacheTimestamp && (now - cacheTimestamp) < CACHE_TTL) {
        return appleConfigCache;
    }

    try {
        const secretsManager = new SecretsManagerClient({
            region: process.env.AWS_REGION || 'us-west-2'
        });

        const command = new GetSecretValueCommand({
            SecretId: secretName
        });

        const result = await secretsManager.send(command);

        if (!result.SecretString) {
            throw new Error('Secret value is empty');
        }

        const config: AppleConfig = JSON.parse(result.SecretString);

        // Validate required fields
        if (!config.teamId || !config.clientId || !config.keyId || !config.privateKey) {
            throw new Error('Apple configuration is missing required fields');
        }

        // Cache the configuration
        appleConfigCache = config;
        cacheTimestamp = now;

        return config;
    } catch (error) {
        console.error('Failed to retrieve Apple configuration from Secrets Manager:', error);
        throw new Error('Failed to retrieve Apple configuration');
    }
}

export {
    getAppleConfig
};

export type {
    AppleConfig
};
