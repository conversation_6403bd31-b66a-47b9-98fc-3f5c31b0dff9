import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const snsClient = new SNSClient(awsConfig);

// Environment variables
const NOTIFICATIONS_TABLE = process.env.NOTIFICATIONS_TABLE!;
const DEVICE_TOKENS_TABLE = process.env.DEVICE_TOKENS_TABLE!;
const NOTIFICATION_PREFERENCES_TABLE = process.env.NOTIFICATION_PREFERENCES_TABLE!;
const NOTIFICATION_HISTORY_TABLE = process.env.NOTIFICATION_HISTORY_TABLE!;
const SNS_TOPIC_ARN = process.env.SNS_TOPIC_ARN!;

// Notification types
export enum NotificationType {
    FOLLOW = 'follow',
    REFLEX = 'reflex',
    COMMENT = 'comment'
}

// Rate limiting configuration (per user per type)
const RATE_LIMITS = {
    [NotificationType.FOLLOW]: {
        maxPerHour: 10,
        maxPerDay: 50
    },
    [NotificationType.REFLEX]: {
        maxPerHour: 20,
        maxPerDay: 100
    },
    [NotificationType.COMMENT]: {
        maxPerHour: 30,
        maxPerDay: 150
    }
};

// Default notification preferences
const DEFAULT_PREFERENCES = {
    [NotificationType.FOLLOW]: true,
    [NotificationType.REFLEX]: true,
    [NotificationType.COMMENT]: true,
    pushEnabled: true,
    emailEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    timezone: 'UTC'
};

export interface NotificationPayload {
    type: NotificationType;
    recipientUserId: string;
    actorUserId: string;
    actorUsername?: string;
    actorDisplayName?: string;
    actorAvatarUrl?: string;
    title: string;
    body: string;
    data?: Record<string, any>;
}

export interface DeviceToken {
    userId: string;
    deviceId: string;
    token: string;
    platform: 'ios' | 'android';
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface NotificationPreferences {
    userId: string;
    follow: boolean;
    reflex: boolean;
    comment: boolean;
    pushEnabled: boolean;
    emailEnabled: boolean;
    quietHoursStart: string;
    quietHoursEnd: string;
    timezone: string;
    updatedAt: string;
}

// Check if user is in quiet hours
const isInQuietHours = (preferences: NotificationPreferences): boolean => {
    const now = new Date();
    const userTime = new Date(now.toLocaleString("en-US", { timeZone: preferences.timezone }));
    const currentHour = userTime.getHours();
    const currentMinute = userTime.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    const [startHour, startMinute] = preferences.quietHoursStart.split(':').map(Number);
    const [endHour, endMinute] = preferences.quietHoursEnd.split(':').map(Number);
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;

    if (startTime <= endTime) {
        // Same day quiet hours (e.g., 22:00 to 23:59)
        return currentTime >= startTime && currentTime <= endTime;
    } else {
        // Overnight quiet hours (e.g., 22:00 to 08:00)
        return currentTime >= startTime || currentTime <= endTime;
    }
};

// Check rate limits for notifications
const checkRateLimit = async (userId: string, type: NotificationType): Promise<boolean> => {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    try {
        // Query recent notifications of this type
        const queryCommand = new QueryCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            IndexName: 'userId-type-index',
            KeyConditionExpression: 'userId = :userId AND #type = :type',
            FilterExpression: '#timestamp > :oneHourAgo',
            ExpressionAttributeNames: {
                '#type': 'type',
                '#timestamp': 'timestamp'
            },
            ExpressionAttributeValues: {
                ':userId': userId,
                ':type': type,
                ':oneHourAgo': oneHourAgo.toISOString()
            }
        });

        const hourlyResult = await dynamodb.send(queryCommand);
        const hourlyCount = hourlyResult.Items?.length || 0;

        if (hourlyCount >= RATE_LIMITS[type].maxPerHour) {
            console.log(`Rate limit exceeded for user ${userId}, type ${type}: ${hourlyCount} notifications in the last hour`);
            return false;
        }

        // Check daily limit
        const dailyQueryCommand = new QueryCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            IndexName: 'userId-type-index',
            KeyConditionExpression: 'userId = :userId AND #type = :type',
            FilterExpression: '#timestamp > :oneDayAgo',
            ExpressionAttributeNames: {
                '#type': 'type',
                '#timestamp': 'timestamp'
            },
            ExpressionAttributeValues: {
                ':userId': userId,
                ':type': type,
                ':oneDayAgo': oneDayAgo.toISOString()
            }
        });

        const dailyResult = await dynamodb.send(dailyQueryCommand);
        const dailyCount = dailyResult.Items?.length || 0;

        if (dailyCount >= RATE_LIMITS[type].maxPerDay) {
            console.log(`Daily rate limit exceeded for user ${userId}, type ${type}: ${dailyCount} notifications in the last day`);
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error checking rate limit:', error);
        return false; // Fail safe - don't send if we can't check
    }
};

// Get user notification preferences
const getUserPreferences = async (userId: string): Promise<NotificationPreferences> => {
    try {
        const getCommand = new GetCommand({
            TableName: NOTIFICATION_PREFERENCES_TABLE,
            Key: { userId }
        });

        const result = await dynamodb.send(getCommand);

        if (result.Item) {
            return result.Item as NotificationPreferences;
        }

        // Return default preferences if none exist
        return {
            userId,
            ...DEFAULT_PREFERENCES,
            updatedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error('Error getting user preferences:', error);
        // Return default preferences on error
        return {
            userId,
            ...DEFAULT_PREFERENCES,
            updatedAt: new Date().toISOString()
        };
    }
};

// Get user device tokens
const getUserDeviceTokens = async (userId: string): Promise<DeviceToken[]> => {
    try {
        const queryCommand = new QueryCommand({
            TableName: DEVICE_TOKENS_TABLE,
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        });

        const result = await dynamodb.send(queryCommand);
        return (result.Items || []) as DeviceToken[];
    } catch (error) {
        console.error('Error getting device tokens:', error);
        return [];
    }
};

// Send push notification via SNS
const sendPushNotification = async (deviceToken: DeviceToken, payload: NotificationPayload): Promise<boolean> => {
    try {
        const message = {
            default: payload.body,
            GCM: JSON.stringify({
                notification: {
                    title: payload.title,
                    body: payload.body,
                },
                data: {
                    type: payload.type,
                    actorUserId: payload.actorUserId,
                    ...payload.data
                }
            }),
            APNS: JSON.stringify({
                aps: {
                    alert: {
                        title: payload.title,
                        body: payload.body,
                    },
                    badge: 1,
                    sound: 'default'
                },
                type: payload.type,
                actorUserId: payload.actorUserId,
                ...payload.data
            })
        };

        const publishCommand = new PublishCommand({
            TopicArn: SNS_TOPIC_ARN,
            Message: JSON.stringify(message),
            MessageStructure: 'json',
            MessageAttributes: {
                platform: {
                    DataType: 'String',
                    StringValue: deviceToken.platform
                },
                userId: {
                    DataType: 'String',
                    StringValue: deviceToken.userId
                }
            }
        });

        await snsClient.send(publishCommand);
        console.log(`Push notification sent to ${deviceToken.platform} device ${deviceToken.deviceId}`);
        return true;
    } catch (error) {
        console.error('Error sending push notification:', error);
        return false;
    }
};

// Main notification sending function
export const sendNotification = async (payload: NotificationPayload): Promise<boolean> => {
    try {
        console.log(`Processing notification: ${payload.type} for user ${payload.recipientUserId} from ${payload.actorUserId}`);

        // Don't send notifications to self
        if (payload.recipientUserId === payload.actorUserId) {
            console.log('Skipping self-notification');
            return false;
        }

        // Check rate limits
        const rateLimitOk = await checkRateLimit(payload.recipientUserId, payload.type);
        if (!rateLimitOk) {
            console.log('Rate limit exceeded, skipping notification');
            return false;
        }

        // Get user preferences
        const preferences = await getUserPreferences(payload.recipientUserId);

        // Check if user has this notification type enabled
        if (!preferences[payload.type] || !preferences.pushEnabled) {
            console.log(`User has disabled ${payload.type} notifications or push notifications`);
            return false;
        }

        // Check quiet hours
        if (isInQuietHours(preferences)) {
            console.log('User is in quiet hours, skipping notification');
            return false;
        }

        // Get device tokens
        const deviceTokens = await getUserDeviceTokens(payload.recipientUserId);
        if (deviceTokens.length === 0) {
            console.log('No active device tokens found for user');
            return false;
        }

        // Create notification record
        const notificationId = require('uuid').v4();
        const now = new Date().toISOString();

        const notificationRecord = {
            id: notificationId,
            userId: payload.recipientUserId,
            type: payload.type,
            actorUserId: payload.actorUserId,
            actorUsername: payload.actorUsername,
            actorDisplayName: payload.actorDisplayName,
            actorAvatarUrl: payload.actorAvatarUrl,
            title: payload.title,
            body: payload.body,
            data: payload.data,
            isRead: false,
            createdAt: now,
            updatedAt: now
        };

        // Save notification to database
        const putNotificationCommand = new PutCommand({
            TableName: NOTIFICATIONS_TABLE,
            Item: notificationRecord
        });
        await dynamodb.send(putNotificationCommand);

        // Record in notification history for rate limiting
        const historyRecord = {
            userId: payload.recipientUserId,
            timestamp: now,
            type: payload.type,
            notificationId,
            ttl: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days TTL
        };

        const putHistoryCommand = new PutCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            Item: historyRecord
        });
        await dynamodb.send(putHistoryCommand);

        // Send push notifications to all devices
        let sentCount = 0;
        for (const deviceToken of deviceTokens) {
            const sent = await sendPushNotification(deviceToken, payload);
            if (sent) sentCount++;
        }

        console.log(`Notification sent to ${sentCount}/${deviceTokens.length} devices`);
        return sentCount > 0;

    } catch (error) {
        console.error('Error sending notification:', error);
        return false;
    }
};

// Helper functions for creating specific notification types
export const createFollowNotification = (
    recipientUserId: string,
    actorUserId: string,
    actorUsername: string,
    actorDisplayName?: string,
    actorAvatarUrl?: string
): NotificationPayload => ({
    type: NotificationType.FOLLOW,
    recipientUserId,
    actorUserId,
    actorUsername,
    actorDisplayName,
    actorAvatarUrl,
    title: 'New Follower',
    body: `${actorDisplayName || actorUsername} started following you`,
    data: {
        followerId: actorUserId,
        followerUsername: actorUsername
    }
});

export const createReflexNotification = (
    recipientUserId: string,
    actorUserId: string,
    actorUsername: string,
    postId: string,
    reflexId: string,
    actorDisplayName?: string,
    actorAvatarUrl?: string
): NotificationPayload => ({
    type: NotificationType.REFLEX,
    recipientUserId,
    actorUserId,
    actorUsername,
    actorDisplayName,
    actorAvatarUrl,
    title: 'New Reflex',
    body: `${actorDisplayName || actorUsername} added a reflex to your post`,
    data: {
        postId,
        reflexId,
        actorUserId
    }
});

export const createCommentNotification = (
    recipientUserId: string,
    actorUserId: string,
    actorUsername: string,
    postId: string,
    commentId: string,
    commentText: string,
    actorDisplayName?: string,
    actorAvatarUrl?: string
): NotificationPayload => ({
    type: NotificationType.COMMENT,
    recipientUserId,
    actorUserId,
    actorUsername,
    actorDisplayName,
    actorAvatarUrl,
    title: 'New Comment',
    body: `${actorDisplayName || actorUsername} commented: ${commentText.length > 50 ? commentText.substring(0, 50) + '...' : commentText}`,
    data: {
        postId,
        commentId,
        actorUserId
    }
});
