{"version": 3, "file": "r2-config.js", "sourceRoot": "", "sources": ["r2-config.ts"], "names": [], "mappings": ";;AAuLI,kCAAW;AACX,wCAAc;AACd,0CAAe;AACf,wCAAc;AACd,oCAAY;AA3LhB,4EAA8F;AAC9F,kDAAuG;AA8BvG,qEAAqE;AACrE,IAAI,aAAa,GAAoB,IAAI,CAAC;AAC1C,IAAI,cAAc,GAAkB,IAAI,CAAC;AACzC,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;AAE7C;;GAEG;AACH,KAAK,UAAU,WAAW;IACtB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,aAAa,CAAC;IAC7D,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IAE9C,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,+DAA+D;QAC/D,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAChF,OAAO;YACH,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;YACzC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;YACjD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;YACjC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YACtC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa;SACvC,CAAC;IACN,CAAC;IAED,+CAA+C;IAE/C,oBAAoB;IACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,IAAI,aAAa,IAAI,cAAc,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,SAAS,EAAE,CAAC;QACxE,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,IAAI,CAAC;QACD,MAAM,cAAc,GAAG,IAAI,6CAAoB,CAAC;YAC5C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;SAChD,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,IAAI,8CAAqB,CAAC;YACtC,QAAQ,EAAE,UAAU;SACvB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,MAAM,GAAa,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAEzD,0BAA0B;QAC1B,aAAa,GAAG,MAAM,CAAC;QACvB,cAAc,GAAG,GAAG,CAAC;QAErB,OAAO,MAAM,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;QAClF,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IAC3D,CAAC;AACL,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc;IACzB,MAAM,MAAM,GAAG,MAAM,WAAW,EAAE,CAAC;IAEnC,sEAAsE;IACtE,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;QACrE,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACnE,OAAO;YACH,YAAY,EAAE,CAAC,SAAiB,EAAE,MAAW,EAAE,EAAE;gBAC7C,OAAO,2BAA2B,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG,cAAc,SAAS,EAAE,CAAC;YAC3F,CAAC;YACD,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1D,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;SACvF,CAAC;IACN,CAAC;IAED,MAAM,QAAQ,GAAG;QACb,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,WAAW,EAAE;YACT,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;SAC1C;QACD,MAAM,EAAE,MAAM,EAAE,2BAA2B;QAC3C,cAAc,EAAE,IAAI;KACvB,CAAC;IAEF,OAAO,IAAI,oBAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe;IAC1B,MAAM,MAAM,GAAG,MAAM,WAAW,EAAE,CAAC;IACnC,OAAO,MAAM,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,sBAAsB,CAAC;AACrF,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc;IACzB,MAAM,MAAM,GAAG,MAAM,WAAW,EAAE,CAAC;IACnC,OAAO,MAAM,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,qDAAqD,CAAC;AAClH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,YAAY;IACvB,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;IAEtD,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,oCAAoC;QACpC,OAAO,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACxF,OAAO;YACH,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;YACpD,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;YAC1C,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;YAChD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW;YAC5D,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;YACpC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;YACpC,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED,IAAI,CAAC;QACD,MAAM,cAAc,GAAG,IAAI,6CAAoB,CAAC;YAC5C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;SAChD,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,IAAI,8CAAqB,CAAC;YACtC,QAAQ,EAAE,UAAU;SACvB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,4DAA4D,EAAE,KAAK,CAAC,CAAC;QACnF,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC5D,CAAC;AACL,CAAC"}