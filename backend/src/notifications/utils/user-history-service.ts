import {
    DynamoD<PERSON>lient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand
} from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

// Initialize DynamoDB client
const dynamodbClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-west-2'
});
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

// Get table name from environment
const USER_HISTORY_TABLE = process.env.USER_HISTORY_TABLE;

// User action types
export enum UserAction {
    // Authentication actions
    ACCOUNT_CREATED = 'account_created',
    USERNAME_SET = 'username_set',
    USERNAME_CHANGED = 'username_changed',
    PASSWORD_RESET_REQUESTED = 'password_reset_requested',
    PASSWORD_RESET_COMPLETED = 'password_reset_completed',
    PASSWORD_CHANGED = 'password_changed',
    EMAIL_VERIFIED = 'email_verified',

    // Third-party authentication
    APPLE_SIGNIN = 'apple_signin',
    APPLE_SIGNUP = 'apple_signup',
    XBOX_SIGNIN = 'xbox_signin',
    XBOX_SIGNUP = 'xbox_signup',
    ACCOUNT_LINKED_XBOX = 'account_linked_xbox',
    ACCOUNT_LINKED_APPLE = 'account_linked_apple',

    // Profile actions
    PROFILE_UPDATED = 'profile_updated',
    AVATAR_CHANGED = 'avatar_changed',
    BIO_UPDATED = 'bio_updated',

    // Content actions
    POST_CREATED = 'post_created',
    POST_PUBLISHED = 'post_published',
    POST_DELETED = 'post_deleted',
    POST_EDITED = 'post_edited',

    // Reflex actions
    REFLEX_CREATED = 'reflex_created',
    REFLEX_DELETED = 'reflex_deleted',

    // Social actions
    USER_FOLLOWED = 'user_followed',
    USER_UNFOLLOWED = 'user_unfollowed',
    COMMENT_CREATED = 'comment_created',

    // Channel actions
    CHANNEL_JOINED = 'channel_joined',
    CHANNEL_LEFT = 'channel_left',
    CHANNEL_CREATED = 'channel_created',

    // Settings actions
    NOTIFICATION_SETTINGS_CHANGED = 'notification_settings_changed',
    PRIVACY_SETTINGS_CHANGED = 'privacy_settings_changed',

    // Moderation actions
    CONTENT_REPORTED = 'content_reported',
    ACCOUNT_SUSPENDED = 'account_suspended',
    ACCOUNT_UNSUSPENDED = 'account_unsuspended'
}

// User history record interface
export interface UserHistoryRecord {
    id: string;
    userId: string;
    action: UserAction;
    message: string;
    timestamp: string;
    metadata?: {
        referenceId?: string;
        beforeValue?: any;
        afterValue?: any;
        ipAddress?: string;
        userAgent?: string;
        additionalData?: Record<string, any>;
    };
}

// Helper function to create a user history record
export const createUserHistoryRecord = (
    userId: string,
    action: UserAction,
    message: string,
    metadata?: UserHistoryRecord['metadata']
): UserHistoryRecord => {
    const timestamp = new Date().toISOString();
    const record: UserHistoryRecord = {
        id: uuidv4(),
        userId,
        action,
        message,
        timestamp,
        metadata
    };

    return record;
};

// Main function to log user action
export const logUserAction = async (
    userId: string,
    action: UserAction,
    message: string,
    metadata?: UserHistoryRecord['metadata']
): Promise<boolean> => {
    try {
        if (!USER_HISTORY_TABLE) {
            console.error('USER_HISTORY_TABLE environment variable not set');
            return false;
        }

        const record = createUserHistoryRecord(userId, action, message, metadata);

        const putCommand = new PutCommand({
            TableName: USER_HISTORY_TABLE,
            Item: record
        });

        await dynamodb.send(putCommand);

        console.log(`User action logged: ${action} for user ${userId}`);
        return true;
    } catch (error) {
        console.error('Failed to log user action:', error);
        return false;
    }
};

// Convenience functions for common actions

export const logAccountCreated = async (
    userId: string,
    email: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.ACCOUNT_CREATED,
        `Account created with email: ${email}`,
        {
            beforeValue: null,
            afterValue: { email },
            ipAddress,
            userAgent
        }
    );
};

export const logUsernameSet = async (
    userId: string,
    username: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.USERNAME_SET,
        `Username set to: ${username}`,
        {
            beforeValue: null,
            afterValue: username,
            ipAddress,
            userAgent
        }
    );
};

export const logUsernameChanged = async (
    userId: string,
    oldUsername: string,
    newUsername: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.USERNAME_CHANGED,
        `Username changed from "${oldUsername}" to "${newUsername}"`,
        {
            beforeValue: oldUsername,
            afterValue: newUsername,
            ipAddress,
            userAgent
        }
    );
};

export const logPasswordResetRequested = async (
    userId: string,
    email: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.PASSWORD_RESET_REQUESTED,
        `Password reset requested for email: ${email}`,
        {
            ipAddress,
            userAgent
        }
    );
};

export const logPasswordResetCompleted = async (
    userId: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.PASSWORD_RESET_COMPLETED,
        'Password reset completed successfully',
        {
            ipAddress,
            userAgent
        }
    );
};

export const logPostCreated = async (
    userId: string,
    postId: string,
    title?: string,
    channelId?: string
): Promise<boolean> => {
    const message = title
        ? `Post created: "${title}" (ID: ${postId})`
        : `Post created with ID: ${postId}`;

    return logUserAction(
        userId,
        UserAction.POST_CREATED,
        message,
        {
            referenceId: postId,
            additionalData: {
                title,
                channelId
            }
        }
    );
};

export const logPostPublished = async (
    userId: string,
    postId: string,
    title?: string
): Promise<boolean> => {
    const message = title
        ? `Post published: "${title}" (ID: ${postId})`
        : `Post published with ID: ${postId}`;

    return logUserAction(
        userId,
        UserAction.POST_PUBLISHED,
        message,
        {
            referenceId: postId,
            additionalData: { title }
        }
    );
};

export const logReflexCreated = async (
    userId: string,
    reflexId: string,
    postId: string,
    reflexType: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.REFLEX_CREATED,
        `${reflexType} reflex created on post ${postId}`,
        {
            referenceId: reflexId,
            additionalData: {
                postId,
                reflexType
            }
        }
    );
};

export const logProfileUpdated = async (
    userId: string,
    changes: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    const changedFields = Object.keys(changes).join(', ');

    return logUserAction(
        userId,
        UserAction.PROFILE_UPDATED,
        `Profile updated: ${changedFields}`,
        {
            beforeValue: changes.before || null,
            afterValue: changes.after || null,
            ipAddress,
            userAgent,
            additionalData: { changedFields: Object.keys(changes) }
        }
    );
};

export const logAppleSignIn = async (
    userId: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.APPLE_SIGNIN,
        `Signed in with Apple${email ? ` (${email})` : ''}`,
        {
            beforeValue: null,
            afterValue: { email },
            ipAddress,
            userAgent
        }
    );
};

export const logAppleSignUp = async (
    userId: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.APPLE_SIGNUP,
        `Signed up with Apple${email ? ` (${email})` : ''}`,
        {
            beforeValue: null,
            afterValue: { email },
            ipAddress,
            userAgent
        }
    );
};

export const logXboxSignIn = async (
    userId: string,
    xboxUserId?: string,
    gamertag?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.XBOX_SIGNIN,
        `Signed in with Xbox${gamertag ? ` (${gamertag})` : ''}`,
        {
            beforeValue: null,
            afterValue: { xboxUserId, gamertag },
            ipAddress,
            userAgent
        }
    );
};

export const logXboxSignUp = async (
    userId: string,
    xboxUserId?: string,
    gamertag?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.XBOX_SIGNUP,
        `Signed up with Xbox${gamertag ? ` (${gamertag})` : ''}`,
        {
            beforeValue: null,
            afterValue: { xboxUserId, gamertag },
            ipAddress,
            userAgent
        }
    );
};

export const logAccountLinkedApple = async (
    userId: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.ACCOUNT_LINKED_APPLE,
        `Linked Apple account${email ? ` (${email})` : ''}`,
        {
            beforeValue: null,
            afterValue: { email },
            ipAddress,
            userAgent
        }
    );
};

export const logAccountLinkedXbox = async (
    userId: string,
    xboxUserId?: string,
    gamertag?: string,
    ipAddress?: string,
    userAgent?: string
): Promise<boolean> => {
    return logUserAction(
        userId,
        UserAction.ACCOUNT_LINKED_XBOX,
        `Linked Xbox account${gamertag ? ` (${gamertag})` : ''}`,
        {
            beforeValue: null,
            afterValue: { xboxUserId, gamertag },
            ipAddress,
            userAgent
        }
    );
};

// Helper function to extract IP address and User Agent from API Gateway event
export const extractRequestMetadata = (event: any) => {
    return {
        ipAddress: event.requestContext?.identity?.sourceIp ||
            event.headers?.['X-Forwarded-For']?.split(',')[0]?.trim() ||
            event.headers?.['x-forwarded-for']?.split(',')[0]?.trim(),
        userAgent: event.headers?.['User-Agent'] || event.headers?.['user-agent']
    };
};
