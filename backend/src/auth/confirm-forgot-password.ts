import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoIdentityProviderClient, ConfirmForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { logPasswordResetCompleted, extractRequestMetadata } from '../utils/user-history-service';

const cognitoClient = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION });

// Initialize DynamoDB client
const dynamodbClient = new DynamoDBClient({ region: process.env.AWS_REGION });
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET'
    },
    body: JSON.stringify(body)
});

interface ConfirmForgotPasswordRequest {
    email: string;
    confirmationCode: string;
    newPassword: string;
}

// Confirm forgot password handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('ConfirmForgotPassword Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, confirmationCode, newPassword }: ConfirmForgotPasswordRequest = JSON.parse(event.body);

        if (!email || !confirmationCode || !newPassword) {
            return createResponse(400, { error: 'Email, confirmation code, and new password are required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Confirm forgot password with new password
        const confirmForgotPasswordCommand = new ConfirmForgotPasswordCommand({
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
            ConfirmationCode: confirmationCode,
            Password: newPassword,
        });

        await cognitoClient.send(confirmForgotPasswordCommand);

        console.log(`Password reset confirmed successfully for: ${normalizedEmail}`);

        // Log password reset completion
        try {
            if (USERS_TABLE) {
                // Look up user by email to get userId for logging
                const queryCommand = new QueryCommand({
                    TableName: USERS_TABLE,
                    IndexName: 'EmailIndex',
                    KeyConditionExpression: 'email = :email',
                    ExpressionAttributeValues: {
                        ':email': normalizedEmail
                    }
                });

                const userResult = await dynamodb.send(queryCommand);
                if (userResult.Items && userResult.Items.length > 0) {
                    const userId = userResult.Items[0].id;
                    const { ipAddress, userAgent } = extractRequestMetadata(event);
                    await logPasswordResetCompleted(userId, ipAddress, userAgent);
                }
            }
        } catch (historyError) {
            console.error('Failed to log password reset completion:', historyError);
            // Don't fail the request if history logging fails
        }

        return createResponse(200, {
            message: 'Password reset successfully',
            details: 'Your password has been updated. You can now sign in with your new password.'
        });

    } catch (error: any) {
        console.error('ConfirmForgotPassword Error:', error);

        // Handle specific Cognito errors
        if (error.name === 'CodeMismatchException') {
            return createResponse(400, {
                error: 'Invalid confirmation code',
                details: 'The confirmation code you entered is incorrect. Please check and try again.'
            });
        } else if (error.name === 'ExpiredCodeException') {
            return createResponse(400, {
                error: 'Confirmation code expired',
                details: 'The confirmation code has expired. Please request a new password reset.'
            });
        } else if (error.name === 'UserNotFoundException') {
            return createResponse(404, {
                error: 'User not found',
                details: 'No account found with this email address.'
            });
        } else if (error.name === 'InvalidPasswordException') {
            return createResponse(400, {
                error: 'Invalid password',
                details: 'Password does not meet the required criteria. Please choose a stronger password.'
            });
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many password reset attempts. Please try again later.'
            });
        } else if (error.name === 'LimitExceededException') {
            return createResponse(429, {
                error: 'Too many requests',
                details: 'Too many password reset attempts. Please wait before trying again.'
            });
        } else {
            return createResponse(500, {
                error: 'Password reset confirmation failed',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
