# API Gateway Module for GameFlex Backend

locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  # API Gateway routes configuration
  api_routes = {
    # Health check (no auth required)
    "GET /health" = {
      function_name = "health"
      auth_required = false
    }
    
    # Authentication routes (no auth required)
    "POST /auth/signup" = {
      function_name = "auth_signup"
      auth_required = false
    }
    "POST /auth/signin" = {
      function_name = "auth"
      auth_required = false
    }
    "POST /auth/confirm" = {
      function_name = "auth_confirm"
      auth_required = false
    }
    "POST /auth/forgot-password" = {
      function_name = "auth_forgot_password"
      auth_required = false
    }
    "POST /auth/reset-password" = {
      function_name = "auth_reset_password"
      auth_required = false
    }
    "POST /auth/refresh" = {
      function_name = "auth"
      auth_required = false
    }

    # Xbox OAuth routes (no auth required)
    "GET /xbox/auth" = {
      function_name = "xbox"
      auth_required = false
    }
    "GET /xbox/callback" = {
      function_name = "xbox"
      auth_required = false
    }
    "POST /xbox/signin" = {
      function_name = "xbox"
      auth_required = false
    }

    # Xbox account management routes (auth required)
    "POST /xbox/link" = {
      function_name = "xbox"
      auth_required = true
    }
    "GET /xbox/account" = {
      function_name = "xbox"
      auth_required = true
    }
    "DELETE /xbox/account" = {
      function_name = "xbox"
      auth_required = true
    }
    "POST /xbox/relink-account" = {
      function_name = "xbox"
      auth_required = true
    }
    "POST /xbox/create-new-account" = {
      function_name = "xbox"
      auth_required = true
    }

    # Xbox media routes (auth required)
    "GET /xbox/screenshots" = {
      function_name = "xbox-media"
      auth_required = true
    }
    "GET /xbox/gameclips" = {
      function_name = "xbox-media"
      auth_required = true
    }
    
    # User routes (auth required)
    "GET /users/me" = {
      function_name = "users"
      auth_required = true
    }
    "PUT /users/me" = {
      function_name = "users"
      auth_required = true
    }
    "POST /users/username" = {
      function_name = "users"
      auth_required = true
    }
    "GET /users/{userId}" = {
      function_name = "users"
      auth_required = true
    }
    "POST /users/{userId}/follow" = {
      function_name = "users"
      auth_required = true
    }
    "DELETE /users/{userId}/follow" = {
      function_name = "users"
      auth_required = true
    }
    
    # Posts routes (auth required)
    "GET /posts" = {
      function_name = "posts"
      auth_required = true
    }
    "POST /posts" = {
      function_name = "posts"
      auth_required = true
    }
    "GET /posts/{postId}" = {
      function_name = "posts"
      auth_required = true
    }
    "PUT /posts/{postId}" = {
      function_name = "posts"
      auth_required = true
    }
    "DELETE /posts/{postId}" = {
      function_name = "posts"
      auth_required = true
    }
    "POST /posts/{postId}/like" = {
      function_name = "posts"
      auth_required = true
    }
    "DELETE /posts/{postId}/like" = {
      function_name = "posts"
      auth_required = true
    }
    "GET /posts/followed" = {
      function_name = "posts"
      auth_required = true
    }
    
    # Reflexes routes (auth required)
    "GET /posts/{postId}/reflexes" = {
      function_name = "reflexes"
      auth_required = true
    }
    "POST /posts/{postId}/reflexes" = {
      function_name = "reflexes"
      auth_required = true
    }
    "PUT /reflexes/{reflexId}" = {
      function_name = "reflexes"
      auth_required = true
    }
    "DELETE /reflexes/{reflexId}" = {
      function_name = "reflexes"
      auth_required = true
    }
    
    # Media routes (auth required)
    "POST /media/upload-url" = {
      function_name = "media"
      auth_required = true
    }
    "POST /media/process" = {
      function_name = "media"
      auth_required = true
    }
    "GET /media/{mediaId}" = {
      function_name = "media"
      auth_required = true
    }
    
    # Channels routes (auth required)
    "GET /channels" = {
      function_name = "channels"
      auth_required = true
    }
    "GET /channels/{channelId}" = {
      function_name = "channels"
      auth_required = true
    }
    "GET /channels/{channelId}/posts" = {
      function_name = "channels"
      auth_required = true
    }
    
    # Notifications routes (auth required)
    "GET /notifications" = {
      function_name = "notifications"
      auth_required = true
    }
    "PUT /notifications/{notificationId}/read" = {
      function_name = "notifications"
      auth_required = true
    }
    "POST /notifications/device-token" = {
      function_name = "notifications"
      auth_required = true
    }
    "PUT /notifications/preferences" = {
      function_name = "notifications"
      auth_required = true
    }

    # Device tokens routes (auth required)
    "POST /device-tokens" = {
      function_name = "notifications"
      auth_required = true
    }
    "DELETE /device-tokens/{deviceId}" = {
      function_name = "notifications"
      auth_required = true
    }

    # Notification preferences routes (auth required)
    "GET /notification-preferences" = {
      function_name = "notifications"
      auth_required = true
    }
    "PUT /notification-preferences" = {
      function_name = "notifications"
      auth_required = true
    }
  }
}

# API Gateway REST API
resource "aws_api_gateway_rest_api" "main" {
  name        = "${local.name_prefix}-api"
  description = "GameFlex Backend API - ${var.environment}"
  
  endpoint_configuration {
    types = ["EDGE"]
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-api"
    Type = "REST API"
  })
}

# API Gateway Authorizer
resource "aws_api_gateway_authorizer" "lambda_authorizer" {
  name                   = "${local.name_prefix}-authorizer"
  rest_api_id           = aws_api_gateway_rest_api.main.id
  authorizer_uri        = var.authorizer_function.invoke_arn
  authorizer_credentials = aws_iam_role.api_gateway_authorizer_role.arn
  type                  = "REQUEST"
  identity_source       = "method.request.header.Authorization"
  authorizer_result_ttl_in_seconds = 300
}

# IAM Role for API Gateway to invoke authorizer
resource "aws_iam_role" "api_gateway_authorizer_role" {
  name = "${local.name_prefix}-api-gateway-authorizer-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for API Gateway authorizer
resource "aws_iam_role_policy" "api_gateway_authorizer_policy" {
  name = "${local.name_prefix}-api-gateway-authorizer-policy"
  role = aws_iam_role.api_gateway_authorizer_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = var.authorizer_function.arn
      }
    ]
  })
}

# Lambda permission for API Gateway to invoke authorizer
resource "aws_lambda_permission" "api_gateway_authorizer" {
  statement_id  = "AllowAPIGatewayInvokeAuthorizer"
  action        = "lambda:InvokeFunction"
  function_name = var.authorizer_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/*/*"
}

# Create main resource groups
resource "aws_api_gateway_resource" "health" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "health"
}

resource "aws_api_gateway_resource" "auth" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "auth"
}

resource "aws_api_gateway_resource" "posts" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "posts"
}

resource "aws_api_gateway_resource" "users" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "users"
}

resource "aws_api_gateway_resource" "channels" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "channels"
}

resource "aws_api_gateway_resource" "reflexes" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "reflexes"
}

resource "aws_api_gateway_resource" "media" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "media"
}

resource "aws_api_gateway_resource" "notifications" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "notifications"
}

resource "aws_api_gateway_resource" "xbox" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "xbox"
}

# Specific sub-resources for auth endpoints
resource "aws_api_gateway_resource" "auth_signup" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "signup"
}

resource "aws_api_gateway_resource" "auth_signin" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "signin"
}

resource "aws_api_gateway_resource" "auth_confirm" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "confirm"
}

resource "aws_api_gateway_resource" "auth_forgot_password" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "forgot-password"
}

resource "aws_api_gateway_resource" "auth_reset_password" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "reset-password"
}

resource "aws_api_gateway_resource" "auth_refresh" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "refresh"
}

resource "aws_api_gateway_resource" "auth_validate" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "validate"
}

resource "aws_api_gateway_resource" "auth_set_username" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "set-username"
}

resource "aws_api_gateway_resource" "auth_check_username" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "check-username-availability"
}

# Device tokens resource
resource "aws_api_gateway_resource" "device_tokens" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "device-tokens"
}

# Device tokens sub-resource for specific device
resource "aws_api_gateway_resource" "device_tokens_device" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.device_tokens.id
  path_part   = "{deviceId}"
}

# Xbox sub-resources
resource "aws_api_gateway_resource" "xbox_auth" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "auth"
}

resource "aws_api_gateway_resource" "xbox_callback" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "callback"
}

resource "aws_api_gateway_resource" "xbox_signin" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "signin"
}

resource "aws_api_gateway_resource" "xbox_link" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "link"
}

resource "aws_api_gateway_resource" "xbox_account" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "account"
}

resource "aws_api_gateway_resource" "xbox_relink_account" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "relink-account"
}

resource "aws_api_gateway_resource" "xbox_create_new_account" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "create-new-account"
}

resource "aws_api_gateway_resource" "xbox_screenshots" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "screenshots"
}

resource "aws_api_gateway_resource" "xbox_gameclips" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.xbox.id
  path_part   = "gameclips"
}

# Posts sub-resources
resource "aws_api_gateway_resource" "posts_draft" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts.id
  path_part   = "draft"
}

resource "aws_api_gateway_resource" "posts_followed" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts.id
  path_part   = "followed"
}

resource "aws_api_gateway_resource" "posts_engagement_feed" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts.id
  path_part   = "engagement-feed"
}

resource "aws_api_gateway_resource" "posts_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts.id
  path_part   = "{id}"
}

resource "aws_api_gateway_resource" "posts_id_media" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "media"
}

resource "aws_api_gateway_resource" "posts_id_publish" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "publish"
}

resource "aws_api_gateway_resource" "posts_id_view" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "view"
}

resource "aws_api_gateway_resource" "posts_id_like" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "like"
}

resource "aws_api_gateway_resource" "posts_id_reactions" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "reactions"
}

resource "aws_api_gateway_resource" "posts_id_comments" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "comments"
}

resource "aws_api_gateway_resource" "posts_id_reflexes" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.posts_id.id
  path_part   = "reflexes"
}

# Notification preferences resource
resource "aws_api_gateway_resource" "notification_preferences" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "notification-preferences"
}

# Media sub-resources
resource "aws_api_gateway_resource" "media_upload" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.media.id
  path_part   = "upload"
}

resource "aws_api_gateway_resource" "media_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.media.id
  path_part   = "{id}"
}

resource "aws_api_gateway_resource" "media_id_process" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.media_id.id
  path_part   = "process"
}

resource "aws_api_gateway_resource" "media_id_analysis" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.media_id.id
  path_part   = "analysis"
}

resource "aws_api_gateway_resource" "media_id_review" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.media_id.id
  path_part   = "review"
}

# Users sub-resources
resource "aws_api_gateway_resource" "users_me" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users.id
  path_part   = "me"
}

resource "aws_api_gateway_resource" "users_me_follow" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_me.id
  path_part   = "follow"
}

resource "aws_api_gateway_resource" "users_me_followers" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_me.id
  path_part   = "followers"
}

resource "aws_api_gateway_resource" "users_me_following" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_me.id
  path_part   = "following"
}

resource "aws_api_gateway_resource" "users_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users.id
  path_part   = "{id}"
}

resource "aws_api_gateway_resource" "users_id_follow" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_id.id
  path_part   = "follow"
}

resource "aws_api_gateway_resource" "users_id_posts" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_id.id
  path_part   = "posts"
}

resource "aws_api_gateway_resource" "users_id_profile" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_id.id
  path_part   = "profile"
}

resource "aws_api_gateway_resource" "users_id_liked_posts" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.users_id.id
  path_part   = "liked-posts"
}

# Channels sub-resources
resource "aws_api_gateway_resource" "channels_my" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.channels.id
  path_part   = "my"
}

resource "aws_api_gateway_resource" "channels_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.channels.id
  path_part   = "{id}"
}

resource "aws_api_gateway_resource" "channels_id_posts" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.channels_id.id
  path_part   = "posts"
}

resource "aws_api_gateway_resource" "channels_id_join" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.channels_id.id
  path_part   = "join"
}

resource "aws_api_gateway_resource" "channels_id_leave" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.channels_id.id
  path_part   = "leave"
}

# Reflexes sub-resources
resource "aws_api_gateway_resource" "reflexes_id" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.reflexes.id
  path_part   = "{id}"
}

resource "aws_api_gateway_resource" "reflexes_id_like" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.reflexes_id.id
  path_part   = "like"
}

resource "aws_api_gateway_resource" "reflexes_id_reactions" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.reflexes_id.id
  path_part   = "reactions"
}

# Health endpoint (no auth required)
resource "aws_api_gateway_method" "health_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.health.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "health_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.health.id
  http_method = aws_api_gateway_method.health_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-health/invocations"
}

# Auth endpoints (no auth required for public endpoints)
resource "aws_api_gateway_method" "auth_signup_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_signup.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_signup_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_signup.id
  http_method = aws_api_gateway_method.auth_signup_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_signup/invocations"
}

resource "aws_api_gateway_method" "auth_signin_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_signin.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_signin_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_signin.id
  http_method = aws_api_gateway_method.auth_signin_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth/invocations"
}

resource "aws_api_gateway_method" "auth_confirm_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_confirm.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_confirm_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_confirm.id
  http_method = aws_api_gateway_method.auth_confirm_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_confirm/invocations"
}

resource "aws_api_gateway_method" "auth_forgot_password_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_forgot_password.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_forgot_password_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_forgot_password.id
  http_method = aws_api_gateway_method.auth_forgot_password_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_forgot_password/invocations"
}

resource "aws_api_gateway_method" "auth_reset_password_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_reset_password.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_reset_password_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_reset_password.id
  http_method = aws_api_gateway_method.auth_reset_password_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_reset_password/invocations"
}

resource "aws_api_gateway_method" "auth_refresh_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_refresh.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "auth_refresh_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_refresh.id
  http_method = aws_api_gateway_method.auth_refresh_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth/invocations"
}

# Protected auth endpoints (require authorization)
resource "aws_api_gateway_method" "auth_validate_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_validate.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "auth_validate_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_validate.id
  http_method = aws_api_gateway_method.auth_validate_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth/invocations"
}

resource "aws_api_gateway_method" "auth_set_username_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_set_username.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "auth_set_username_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_set_username.id
  http_method = aws_api_gateway_method.auth_set_username_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_set_username/invocations"
}

resource "aws_api_gateway_method" "auth_check_username_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.auth_check_username.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "auth_check_username_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.auth_check_username.id
  http_method = aws_api_gateway_method.auth_check_username_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-auth_set_username/invocations"
}

# Xbox endpoints (no auth required for auth, callback, signin)
resource "aws_api_gateway_method" "xbox_auth_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_auth.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "xbox_auth_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_auth.id
  http_method = aws_api_gateway_method.xbox_auth_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_callback_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_callback.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "xbox_callback_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_callback.id
  http_method = aws_api_gateway_method.xbox_callback_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_signin_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_signin.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "xbox_signin_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_signin.id
  http_method = aws_api_gateway_method.xbox_signin_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

# Posts endpoints (auth required)
resource "aws_api_gateway_method" "posts_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts.id
  http_method = aws_api_gateway_method.posts_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts.id
  http_method = aws_api_gateway_method.posts_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts sub-endpoints
resource "aws_api_gateway_method" "posts_draft_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_draft.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_draft_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_draft.id
  http_method = aws_api_gateway_method.posts_draft_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id} methods
resource "aws_api_gateway_method" "posts_id_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id.id
  http_method = aws_api_gateway_method.posts_id_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_id_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id.id
  http_method = aws_api_gateway_method.posts_id_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/media methods
resource "aws_api_gateway_method" "posts_id_media_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_media.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_media_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_media.id
  http_method = aws_api_gateway_method.posts_id_media_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/publish methods
resource "aws_api_gateway_method" "posts_id_publish_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_publish.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_publish_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_publish.id
  http_method = aws_api_gateway_method.posts_id_publish_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/view methods
resource "aws_api_gateway_method" "posts_id_view_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_view.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_view_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_view.id
  http_method = aws_api_gateway_method.posts_id_view_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/like methods
resource "aws_api_gateway_method" "posts_id_like_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_like.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_like_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_like.id
  http_method = aws_api_gateway_method.posts_id_like_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_id_like_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_like.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_like_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_like.id
  http_method = aws_api_gateway_method.posts_id_like_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/reactions methods
resource "aws_api_gateway_method" "posts_id_reactions_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_reactions.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_reactions_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_reactions.id
  http_method = aws_api_gateway_method.posts_id_reactions_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_id_reactions_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_reactions.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_reactions_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_reactions.id
  http_method = aws_api_gateway_method.posts_id_reactions_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/comments methods
resource "aws_api_gateway_method" "posts_id_comments_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_comments.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_comments_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_comments.id
  http_method = aws_api_gateway_method.posts_id_comments_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_id_comments_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_comments.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_comments_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_comments.id
  http_method = aws_api_gateway_method.posts_id_comments_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Posts/{id}/reflexes methods
resource "aws_api_gateway_method" "posts_id_reflexes_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_reflexes.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_reflexes_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_reflexes.id
  http_method = aws_api_gateway_method.posts_id_reflexes_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

resource "aws_api_gateway_method" "posts_id_reflexes_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_id_reflexes.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_id_reflexes_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_id_reflexes.id
  http_method = aws_api_gateway_method.posts_id_reflexes_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

# Media methods
resource "aws_api_gateway_method" "media_upload_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_upload.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_upload_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_upload.id
  http_method = aws_api_gateway_method.media_upload_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Media/{id} methods
resource "aws_api_gateway_method" "media_id_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id.id
  http_method = aws_api_gateway_method.media_id_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

resource "aws_api_gateway_method" "media_id_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id.id
  http_method = aws_api_gateway_method.media_id_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

resource "aws_api_gateway_method" "media_id_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id.id
  http_method = aws_api_gateway_method.media_id_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Media/{id}/process methods
resource "aws_api_gateway_method" "media_id_process_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id_process.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_process_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id_process.id
  http_method = aws_api_gateway_method.media_id_process_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Media/{id}/analysis methods
resource "aws_api_gateway_method" "media_id_analysis_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id_analysis.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_analysis_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id_analysis.id
  http_method = aws_api_gateway_method.media_id_analysis_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Media/{id}/review methods
resource "aws_api_gateway_method" "media_id_review_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media_id_review.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media_id_review_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media_id_review.id
  http_method = aws_api_gateway_method.media_id_review_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Users methods
resource "aws_api_gateway_method" "users_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users.id
  http_method = aws_api_gateway_method.users_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/me methods
resource "aws_api_gateway_method" "users_me_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_me.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_me_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_me.id
  http_method = aws_api_gateway_method.users_me_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

resource "aws_api_gateway_method" "users_me_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_me.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_me_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_me.id
  http_method = aws_api_gateway_method.users_me_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/me/follow methods
resource "aws_api_gateway_method" "users_me_follow_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_me_follow.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_me_follow_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_me_follow.id
  http_method = aws_api_gateway_method.users_me_follow_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/me/followers methods
resource "aws_api_gateway_method" "users_me_followers_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_me_followers.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_me_followers_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_me_followers.id
  http_method = aws_api_gateway_method.users_me_followers_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/me/following methods
resource "aws_api_gateway_method" "users_me_following_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_me_following.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_me_following_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_me_following.id
  http_method = aws_api_gateway_method.users_me_following_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/{id} methods
resource "aws_api_gateway_method" "users_id_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id.id
  http_method = aws_api_gateway_method.users_id_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/{id}/profile methods
resource "aws_api_gateway_method" "users_id_profile_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_profile.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_profile_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_profile.id
  http_method = aws_api_gateway_method.users_id_profile_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

resource "aws_api_gateway_method" "users_id_profile_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_profile.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_profile_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_profile.id
  http_method = aws_api_gateway_method.users_id_profile_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/{id}/liked-posts methods
resource "aws_api_gateway_method" "users_id_liked_posts_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_liked_posts.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_liked_posts_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_liked_posts.id
  http_method = aws_api_gateway_method.users_id_liked_posts_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/{id}/posts methods
resource "aws_api_gateway_method" "users_id_posts_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_posts.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_posts_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_posts.id
  http_method = aws_api_gateway_method.users_id_posts_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Users/{id}/follow methods
resource "aws_api_gateway_method" "users_id_follow_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_follow.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_follow_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_follow.id
  http_method = aws_api_gateway_method.users_id_follow_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

resource "aws_api_gateway_method" "users_id_follow_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.users_id_follow.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "users_id_follow_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.users_id_follow.id
  http_method = aws_api_gateway_method.users_id_follow_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-users/invocations"
}

# Channels methods
resource "aws_api_gateway_method" "channels_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels.id
  http_method = aws_api_gateway_method.channels_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

resource "aws_api_gateway_method" "channels_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels.id
  http_method = aws_api_gateway_method.channels_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Channels/my methods
resource "aws_api_gateway_method" "channels_my_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_my.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_my_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_my.id
  http_method = aws_api_gateway_method.channels_my_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Channels/{id} methods
resource "aws_api_gateway_method" "channels_id_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id.id
  http_method = aws_api_gateway_method.channels_id_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

resource "aws_api_gateway_method" "channels_id_put" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id.id
  http_method   = "PUT"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_put" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id.id
  http_method = aws_api_gateway_method.channels_id_put.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

resource "aws_api_gateway_method" "channels_id_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id.id
  http_method = aws_api_gateway_method.channels_id_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Channels/{id}/posts methods
resource "aws_api_gateway_method" "channels_id_posts_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id_posts.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_posts_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id_posts.id
  http_method = aws_api_gateway_method.channels_id_posts_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Channels/{id}/join methods
resource "aws_api_gateway_method" "channels_id_join_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id_join.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_join_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id_join.id
  http_method = aws_api_gateway_method.channels_id_join_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Channels/{id}/leave methods
resource "aws_api_gateway_method" "channels_id_leave_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.channels_id_leave.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "channels_id_leave_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.channels_id_leave.id
  http_method = aws_api_gateway_method.channels_id_leave_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-channels/invocations"
}

# Reflexes methods
resource "aws_api_gateway_method" "reflexes_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes.id
  http_method = aws_api_gateway_method.reflexes_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

resource "aws_api_gateway_method" "reflexes_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes.id
  http_method = aws_api_gateway_method.reflexes_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

# Reflexes/{id} methods
resource "aws_api_gateway_method" "reflexes_id_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes_id.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_id_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes_id.id
  http_method = aws_api_gateway_method.reflexes_id_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

# Reflexes/{id}/like methods
resource "aws_api_gateway_method" "reflexes_id_like_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes_id_like.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_id_like_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes_id_like.id
  http_method = aws_api_gateway_method.reflexes_id_like_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

resource "aws_api_gateway_method" "reflexes_id_like_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes_id_like.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_id_like_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes_id_like.id
  http_method = aws_api_gateway_method.reflexes_id_like_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

# Reflexes/{id}/reactions methods
resource "aws_api_gateway_method" "reflexes_id_reactions_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes_id_reactions.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_id_reactions_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes_id_reactions.id
  http_method = aws_api_gateway_method.reflexes_id_reactions_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

resource "aws_api_gateway_method" "reflexes_id_reactions_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.reflexes_id_reactions.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "reflexes_id_reactions_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.reflexes_id_reactions.id
  http_method = aws_api_gateway_method.reflexes_id_reactions_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-reflexes/invocations"
}

# Notifications methods
resource "aws_api_gateway_method" "notifications_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.notifications.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "notifications_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.notifications.id
  http_method = aws_api_gateway_method.notifications_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-notifications/invocations"
}

# Xbox link method (protected)
resource "aws_api_gateway_method" "xbox_link_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_link.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_link_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_link.id
  http_method = aws_api_gateway_method.xbox_link_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

# Xbox account management endpoints
resource "aws_api_gateway_method" "xbox_account_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_account.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_account_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_account.id
  http_method = aws_api_gateway_method.xbox_account_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_account_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_account.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_account_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_account.id
  http_method = aws_api_gateway_method.xbox_account_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_relink_account_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_relink_account.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_relink_account_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_relink_account.id
  http_method = aws_api_gateway_method.xbox_relink_account_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_create_new_account_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_create_new_account.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_create_new_account_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_create_new_account.id
  http_method = aws_api_gateway_method.xbox_create_new_account_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

# Xbox media endpoints
resource "aws_api_gateway_method" "xbox_screenshots_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_screenshots.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_screenshots_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_screenshots.id
  http_method = aws_api_gateway_method.xbox_screenshots_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "xbox_gameclips_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.xbox_gameclips.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "xbox_gameclips_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.xbox_gameclips.id
  http_method = aws_api_gateway_method.xbox_gameclips_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-xbox/invocations"
}

resource "aws_api_gateway_method" "posts_followed_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_followed.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_followed_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_followed.id
  http_method = aws_api_gateway_method.posts_followed_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

resource "aws_api_gateway_method" "posts_engagement_feed_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.posts_engagement_feed.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "posts_engagement_feed_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.posts_engagement_feed.id
  http_method = aws_api_gateway_method.posts_engagement_feed_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-posts/invocations"
}

# Media endpoints (auth required)
resource "aws_api_gateway_method" "media" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.media.id
  http_method   = "ANY"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "media" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.media.id
  http_method = aws_api_gateway_method.media.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-media/invocations"
}

# Device tokens methods and integrations
resource "aws_api_gateway_method" "device_tokens_post" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.device_tokens.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "device_tokens_post" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.device_tokens.id
  http_method = aws_api_gateway_method.device_tokens_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-notifications/invocations"
}

resource "aws_api_gateway_method" "device_tokens_device_delete" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.device_tokens_device.id
  http_method   = "DELETE"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "device_tokens_device_delete" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.device_tokens_device.id
  http_method = aws_api_gateway_method.device_tokens_device_delete.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-notifications/invocations"
}

# Notification preferences methods and integrations
resource "aws_api_gateway_method" "notification_preferences_get" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.notification_preferences.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "notification_preferences_get" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.notification_preferences.id
  http_method = aws_api_gateway_method.notification_preferences_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = "arn:aws:apigateway:${data.aws_region.current.name}:lambda:path/2015-03-31/functions/arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.project_name}-${var.environment}-notifications/invocations"
}

# Lambda permissions for API Gateway to invoke functions
resource "aws_lambda_permission" "api_gateway_lambda" {
  for_each = toset([
    "auth", "auth_signup", "auth_confirm", "auth_forgot_password", "auth_reset_password", "auth_set_username",
    "authorizer", "channels", "health", "media", "posts",
    "reflexes", "users", "notifications", "ai_processing",
    "media_processor", "xbox"
  ])

  statement_id  = "AllowAPIGatewayInvoke-${each.key}"
  action        = "lambda:InvokeFunction"
  function_name = "${var.project_name}-${var.environment}-${each.key}"
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/*/*"
}

# API Gateway Deployment
resource "aws_api_gateway_deployment" "main" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  stage_name  = "api"

  # Force new deployment when any method or integration changes
  triggers = {
    redeployment = sha1(jsonencode([
      # Force redeployment for auth integration changes
      # Health
      aws_api_gateway_method.health_get.id,
      aws_api_gateway_integration.health_get.id,
      # Xbox endpoints
      aws_api_gateway_method.xbox_auth_get.id,
      aws_api_gateway_integration.xbox_auth_get.id,
      aws_api_gateway_method.xbox_callback_get.id,
      aws_api_gateway_integration.xbox_callback_get.id,
      aws_api_gateway_method.xbox_signin_post.id,
      aws_api_gateway_integration.xbox_signin_post.id,
      aws_api_gateway_method.xbox_link_post.id,
      aws_api_gateway_integration.xbox_link_post.id,
      aws_api_gateway_method.xbox_account_get.id,
      aws_api_gateway_integration.xbox_account_get.id,
      aws_api_gateway_method.xbox_account_delete.id,
      aws_api_gateway_integration.xbox_account_delete.id,
      aws_api_gateway_method.xbox_relink_account_post.id,
      aws_api_gateway_integration.xbox_relink_account_post.id,
      aws_api_gateway_method.xbox_create_new_account_post.id,
      aws_api_gateway_integration.xbox_create_new_account_post.id,
      aws_api_gateway_method.xbox_screenshots_get.id,
      aws_api_gateway_integration.xbox_screenshots_get.id,
      aws_api_gateway_method.xbox_gameclips_get.id,
      aws_api_gateway_integration.xbox_gameclips_get.id,
      # Auth endpoints
      aws_api_gateway_method.auth_signup_post.id,
      aws_api_gateway_integration.auth_signup_post.id,
      aws_api_gateway_method.auth_signin_post.id,
      aws_api_gateway_integration.auth_signin_post.id,
      aws_api_gateway_method.auth_confirm_post.id,
      aws_api_gateway_integration.auth_confirm_post.id,
      aws_api_gateway_method.auth_forgot_password_post.id,
      aws_api_gateway_integration.auth_forgot_password_post.id,
      aws_api_gateway_method.auth_reset_password_post.id,
      aws_api_gateway_integration.auth_reset_password_post.id,
      aws_api_gateway_method.auth_refresh_post.id,
      aws_api_gateway_integration.auth_refresh_post.id,
      aws_api_gateway_method.auth_validate_get.id,
      aws_api_gateway_integration.auth_validate_get.id,
      aws_api_gateway_method.auth_set_username_post.id,
      aws_api_gateway_integration.auth_set_username_post.id,
      aws_api_gateway_method.auth_check_username_get.id,
      aws_api_gateway_integration.auth_check_username_get.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }

  depends_on = [
    # Health
    aws_api_gateway_method.health_get,
    aws_api_gateway_integration.health_get,
    # Auth
    aws_api_gateway_method.auth_signup_post,
    aws_api_gateway_integration.auth_signup_post,
    aws_api_gateway_method.auth_signin_post,
    aws_api_gateway_integration.auth_signin_post,
    aws_api_gateway_method.auth_confirm_post,
    aws_api_gateway_integration.auth_confirm_post,
    aws_api_gateway_method.auth_forgot_password_post,
    aws_api_gateway_integration.auth_forgot_password_post,
    aws_api_gateway_method.auth_reset_password_post,
    aws_api_gateway_integration.auth_reset_password_post,
    aws_api_gateway_method.auth_refresh_post,
    aws_api_gateway_integration.auth_refresh_post,
    aws_api_gateway_method.auth_validate_get,
    aws_api_gateway_integration.auth_validate_get,
    aws_api_gateway_method.auth_set_username_post,
    aws_api_gateway_integration.auth_set_username_post,
    aws_api_gateway_method.auth_check_username_get,
    aws_api_gateway_integration.auth_check_username_get,
    # Xbox
    aws_api_gateway_method.xbox_auth_get,
    aws_api_gateway_integration.xbox_auth_get,
    aws_api_gateway_method.xbox_callback_get,
    aws_api_gateway_integration.xbox_callback_get,
    aws_api_gateway_method.xbox_signin_post,
    aws_api_gateway_integration.xbox_signin_post,
    aws_api_gateway_method.xbox_link_post,
    aws_api_gateway_integration.xbox_link_post,
    aws_api_gateway_method.xbox_account_get,
    aws_api_gateway_integration.xbox_account_get,
    aws_api_gateway_method.xbox_account_delete,
    aws_api_gateway_integration.xbox_account_delete,
    aws_api_gateway_method.xbox_relink_account_post,
    aws_api_gateway_integration.xbox_relink_account_post,
    aws_api_gateway_method.xbox_create_new_account_post,
    aws_api_gateway_integration.xbox_create_new_account_post,
    aws_api_gateway_method.xbox_screenshots_get,
    aws_api_gateway_integration.xbox_screenshots_get,
    aws_api_gateway_method.xbox_gameclips_get,
    aws_api_gateway_integration.xbox_gameclips_get,
    # Posts
    aws_api_gateway_method.posts_get,
    aws_api_gateway_integration.posts_get,
    aws_api_gateway_method.posts_post,
    aws_api_gateway_integration.posts_post,
    aws_api_gateway_method.posts_draft_post,
    aws_api_gateway_integration.posts_draft_post,
    aws_api_gateway_method.posts_followed_get,
    aws_api_gateway_integration.posts_followed_get,
    aws_api_gateway_method.posts_engagement_feed_get,
    aws_api_gateway_integration.posts_engagement_feed_get,
    aws_api_gateway_method.posts_id_get,
    aws_api_gateway_integration.posts_id_get,
    aws_api_gateway_method.posts_id_delete,
    aws_api_gateway_integration.posts_id_delete,
    aws_api_gateway_method.posts_id_media_put,
    aws_api_gateway_integration.posts_id_media_put,
    aws_api_gateway_method.posts_id_publish_put,
    aws_api_gateway_integration.posts_id_publish_put,
    aws_api_gateway_method.posts_id_view_post,
    aws_api_gateway_integration.posts_id_view_post,
    aws_api_gateway_method.posts_id_like_post,
    aws_api_gateway_integration.posts_id_like_post,
    aws_api_gateway_method.posts_id_like_delete,
    aws_api_gateway_integration.posts_id_like_delete,
    aws_api_gateway_method.posts_id_reactions_post,
    aws_api_gateway_integration.posts_id_reactions_post,
    aws_api_gateway_method.posts_id_reactions_delete,
    aws_api_gateway_integration.posts_id_reactions_delete,
    aws_api_gateway_method.posts_id_comments_get,
    aws_api_gateway_integration.posts_id_comments_get,
    aws_api_gateway_method.posts_id_comments_post,
    aws_api_gateway_integration.posts_id_comments_post,
    aws_api_gateway_method.posts_id_reflexes_get,
    aws_api_gateway_integration.posts_id_reflexes_get,
    aws_api_gateway_method.posts_id_reflexes_post,
    aws_api_gateway_integration.posts_id_reflexes_post,
    # Media
    aws_api_gateway_method.media_upload_post,
    aws_api_gateway_integration.media_upload_post,
    aws_api_gateway_method.media_id_get,
    aws_api_gateway_integration.media_id_get,
    aws_api_gateway_method.media_id_process_post,
    aws_api_gateway_integration.media_id_process_post,
    aws_api_gateway_method.media_id_analysis_get,
    aws_api_gateway_integration.media_id_analysis_get,
    aws_api_gateway_method.media_id_review_post,
    aws_api_gateway_integration.media_id_review_post,
    # Users
    aws_api_gateway_method.users_get,
    aws_api_gateway_integration.users_get,
    aws_api_gateway_method.users_me_get,
    aws_api_gateway_integration.users_me_get,
    aws_api_gateway_method.users_me_put,
    aws_api_gateway_integration.users_me_put,
    aws_api_gateway_method.users_me_follow_get,
    aws_api_gateway_integration.users_me_follow_get,
    aws_api_gateway_method.users_me_followers_get,
    aws_api_gateway_integration.users_me_followers_get,
    aws_api_gateway_method.users_me_following_get,
    aws_api_gateway_integration.users_me_following_get,
    aws_api_gateway_method.users_id_get,
    aws_api_gateway_integration.users_id_get,
    aws_api_gateway_method.users_id_profile_get,
    aws_api_gateway_integration.users_id_profile_get,
    aws_api_gateway_method.users_id_profile_put,
    aws_api_gateway_integration.users_id_profile_put,
    aws_api_gateway_method.users_id_liked_posts_get,
    aws_api_gateway_integration.users_id_liked_posts_get,
    aws_api_gateway_method.users_id_posts_get,
    aws_api_gateway_integration.users_id_posts_get,
    aws_api_gateway_method.users_id_follow_post,
    aws_api_gateway_integration.users_id_follow_post,
    aws_api_gateway_method.users_id_follow_delete,
    aws_api_gateway_integration.users_id_follow_delete,
    # Channels
    aws_api_gateway_method.channels_get,
    aws_api_gateway_integration.channels_get,
    aws_api_gateway_method.channels_post,
    aws_api_gateway_integration.channels_post,
    aws_api_gateway_method.channels_my_get,
    aws_api_gateway_integration.channels_my_get,
    aws_api_gateway_method.channels_id_get,
    aws_api_gateway_integration.channels_id_get,
    aws_api_gateway_method.channels_id_put,
    aws_api_gateway_integration.channels_id_put,
    aws_api_gateway_method.channels_id_delete,
    aws_api_gateway_integration.channels_id_delete,
    aws_api_gateway_method.channels_id_posts_get,
    aws_api_gateway_integration.channels_id_posts_get,
    aws_api_gateway_method.channels_id_join_post,
    aws_api_gateway_integration.channels_id_join_post,
    aws_api_gateway_method.channels_id_leave_post,
    aws_api_gateway_integration.channels_id_leave_post,
    # Reflexes
    aws_api_gateway_method.reflexes_get,
    aws_api_gateway_integration.reflexes_get,
    aws_api_gateway_method.reflexes_post,
    aws_api_gateway_integration.reflexes_post,
    aws_api_gateway_method.reflexes_id_delete,
    aws_api_gateway_integration.reflexes_id_delete,
    aws_api_gateway_method.reflexes_id_like_post,
    aws_api_gateway_integration.reflexes_id_like_post,
    aws_api_gateway_method.reflexes_id_like_delete,
    aws_api_gateway_integration.reflexes_id_like_delete,
    aws_api_gateway_method.reflexes_id_reactions_post,
    aws_api_gateway_integration.reflexes_id_reactions_post,
    aws_api_gateway_method.reflexes_id_reactions_delete,
    aws_api_gateway_integration.reflexes_id_reactions_delete,
    # Notifications
    aws_api_gateway_method.notifications_get,
    aws_api_gateway_integration.notifications_get,
    aws_api_gateway_method.device_tokens_post,
    aws_api_gateway_integration.device_tokens_post,
    aws_api_gateway_method.device_tokens_device_delete,
    aws_api_gateway_integration.device_tokens_device_delete,
    aws_api_gateway_method.notification_preferences_get,
    aws_api_gateway_integration.notification_preferences_get
  ]
}

# Note: Removed API Gateway Stage to avoid deployment issues
# API Gateway will work without explicit stages

# CloudWatch Log Group for API Gateway (if logging enabled)
resource "aws_cloudwatch_log_group" "api_gateway" {
  count = var.enable_logging ? 1 : 0

  name              = "/aws/apigateway/${local.name_prefix}"
  retention_in_days = var.log_retention_days

  tags = var.tags
}

# Data source for current region and account
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Custom Domain Name (if provided)
resource "aws_api_gateway_domain_name" "main" {
  count           = var.domain_name != "" ? 1 : 0
  domain_name     = var.domain_name
  certificate_arn = var.certificate_arn

  endpoint_configuration {
    types = ["EDGE"]
  }

  tags = var.tags
}

# Base Path Mapping for custom domain
resource "aws_api_gateway_base_path_mapping" "main" {
  count       = var.domain_name != "" ? 1 : 0
  api_id      = aws_api_gateway_rest_api.main.id
  stage_name  = aws_api_gateway_deployment.main.stage_name
  domain_name = aws_api_gateway_domain_name.main[0].domain_name

  lifecycle {
    create_before_destroy = true
  }
}

# Route53 Record for custom domain
data "aws_route53_zone" "main" {
  count = var.domain_name != "" ? 1 : 0
  name  = "gameflex.io"
}

resource "aws_route53_record" "api" {
  count   = var.domain_name != "" ? 1 : 0
  zone_id = data.aws_route53_zone.main[0].zone_id
  name    = var.domain_name
  type    = "A"

  alias {
    name                   = aws_api_gateway_domain_name.main[0].cloudfront_domain_name
    zone_id                = aws_api_gateway_domain_name.main[0].cloudfront_zone_id
    evaluate_target_health = false
  }
}


